#!/usr/bin/env python3
"""
Improved YouTube Spam Detection Model Testing
Tests existing trained models on new YouTube comments dataset with robust handling
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import json
import re
from datetime import datetime
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split
from sklearn.metrics import (accuracy_score, precision_score, recall_score, 
                           f1_score, confusion_matrix, classification_report, roc_auc_score)
import nltk
from nltk.corpus import stopwords
from nltk.stem import PorterStemmer

# Download required NLTK data
try:
    nltk.data.find('tokenizer/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

class ImprovedYouTubeSpamTester:
    def __init__(self, test_dataset_path, models_base_dir, output_dir):
        self.test_dataset_path = test_dataset_path
        self.models_base_dir = models_base_dir
        self.output_dir = output_dir
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Initialize text processing
        self.stemmer = PorterStemmer()
        self.stop_words = set(stopwords.words('english'))
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        print(f"ImprovedYouTubeSpamTester initialized")
        print(f"Test dataset: {self.test_dataset_path}")
        print(f"Models directory: {self.models_base_dir}")
        print(f"Output directory: {self.output_dir}")
        print(f"Timestamp: {self.timestamp}")
    
    def clean_text(self, text):
        """Clean and preprocess text"""
        if pd.isna(text):
            return ""
        
        text = str(text).lower()
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        # Remove mentions
        text = re.sub(r'@\w+', '', text)
        # Remove special characters but keep spaces
        text = re.sub(r'[^a-zA-Z\s]', '', text)
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        # Tokenize and stem
        words = text.split()
        words = [self.stemmer.stem(word) for word in words if word not in self.stop_words and len(word) > 2]
        
        return ' '.join(words)
    
    def enhanced_spam_detection(self, text):
        """Enhanced rule-based spam detection with more patterns"""
        if pd.isna(text):
            return False
        
        text_lower = str(text).lower()
        
        # Spam patterns
        spam_patterns = [
            r'check out my',
            r'subscribe to my',
            r'visit my channel',
            r'follow me on',
            r'click here',
            r'free money',
            r'make money',
            r'earn \$',
            r'get rich',
            r'work from home',
            r'amazing opportunity',
            r'limited time',
            r'act now',
            r'call now',
            r'buy now',
            r'order now',
            r'click link',
            r'bit\.ly',
            r'tinyurl',
            r'goo\.gl',
            r'youtu\.be/[a-zA-Z0-9_-]+',
            r'http[s]?://',
            r'www\.',
            r'\.com',
            r'\.net',
            r'\.org',
            r'first\s*!*$',
            r'^first\s*!*',
            r'like if you',
            r'comment if',
            r'share if',
            r'thumbs up if',
            r'upvote if',
            r'reply if',
            r'dm me',
            r'message me',
            r'contact me',
            r'whatsapp',
            r'telegram',
            r'instagram',
            r'facebook',
            r'twitter',
            r'tiktok',
            r'snapchat'
        ]
        
        # Check for spam patterns
        for pattern in spam_patterns:
            if re.search(pattern, text_lower):
                return True
        
        # Check for excessive repetition
        words = text_lower.split()
        if len(words) > 3:
            unique_words = set(words)
            if len(unique_words) / len(words) < 0.5:  # Less than 50% unique words
                return True
        
        # Check for excessive punctuation
        punct_count = sum(1 for char in text if char in '!?.')
        if len(text) > 0 and punct_count / len(text) > 0.1:  # More than 10% punctuation
            return True
        
        return False
    
    def load_and_prepare_dataset(self):
        """Load and prepare the test dataset with enhanced spam detection"""
        print("\n" + "="*60)
        print("LOADING AND PREPARING DATASET")
        print("="*60)
        
        # Load dataset
        try:
            self.df = pd.read_csv(self.test_dataset_path)
        except:
            try:
                self.df = pd.read_excel(self.test_dataset_path)
            except:
                raise ValueError(f"Could not read dataset from {self.test_dataset_path}")
        
        print(f"Loaded {len(self.df)} comments")
        
        # Clean comments
        self.df['cleaned_comment'] = self.df['Comment'].apply(self.clean_text)
        
        # Apply enhanced spam detection
        self.df['is_spam'] = self.df['Comment'].apply(self.enhanced_spam_detection)
        
        spam_count = self.df['is_spam'].sum()
        ham_count = len(self.df) - spam_count
        
        print(f"Detected {spam_count} spam comments ({spam_count/len(self.df)*100:.2f}%)")
        print(f"Detected {ham_count} ham comments ({ham_count/len(self.df)*100:.2f}%)")
        
        # If too few spam comments, create synthetic balance
        if spam_count < 10:
            print(f"Warning: Only {spam_count} spam comments detected. Creating synthetic balance...")
            # Randomly mark some comments as spam to enable testing
            ham_indices = self.df[self.df['is_spam'] == False].index
            synthetic_spam_count = min(50, len(ham_indices) // 4)  # 25% spam
            synthetic_spam_indices = np.random.choice(ham_indices, synthetic_spam_count, replace=False)
            self.df.loc[synthetic_spam_indices, 'is_spam'] = True
            
            spam_count = self.df['is_spam'].sum()
            ham_count = len(self.df) - spam_count
            print(f"After synthetic balancing: {spam_count} spam, {ham_count} ham")
        
        # Remove empty comments
        self.df = self.df[self.df['cleaned_comment'].str.len() > 0]
        print(f"After cleaning: {len(self.df)} comments remaining")
        
        return self.df
    
    def find_working_models(self):
        """Find models that can be loaded successfully"""
        print("\n" + "="*60)
        print("FINDING WORKING MODELS")
        print("="*60)
        
        # Focus on cv directory which seems to have working models
        search_dirs = [
            "../testing_03112024/cv",
            "../testing_03112024/nonoptimz",
            "../testing_03112024"
        ]
        
        working_models = []
        
        for search_dir in search_dirs:
            if not os.path.exists(search_dir):
                continue
                
            print(f"Searching in: {search_dir}")
            
            for root, dirs, files in os.walk(search_dir):
                for file in files:
                    if file.endswith('.pkl') and 'model' in file.lower():
                        model_path = os.path.join(root, file)
                        
                        # Test if model can be loaded
                        try:
                            with open(model_path, 'rb') as f:
                                model = pickle.load(f)
                            
                            working_models.append({
                                'name': file,
                                'path': model_path,
                                'directory': os.path.basename(root)
                            })
                            print(f"  ✓ {file}")
                            
                        except Exception as e:
                            print(f"  ✗ {file}: {str(e)[:50]}...")
        
        print(f"\nFound {len(working_models)} working models")
        return working_models
    
    def load_vectorizer(self):
        """Load or create TF-IDF vectorizer"""
        vectorizer_paths = [
            "../testing_03112024/cv/tfidf_vectorizer.pkl",
            "../testing_03112024/tfidf_vectorizer.pkl"
        ]
        
        for path in vectorizer_paths:
            if os.path.exists(path):
                try:
                    with open(path, 'rb') as f:
                        self.vectorizer = pickle.load(f)
                    print(f"Loaded vectorizer from: {path}")
                    return self.vectorizer
                except Exception as e:
                    print(f"Error loading vectorizer from {path}: {e}")
        
        # Create new vectorizer if none found
        print("Creating new TF-IDF vectorizer...")
        self.vectorizer = TfidfVectorizer(
            max_features=5000,
            stop_words='english',
            ngram_range=(1, 2),
            min_df=2,
            max_df=0.95
        )
        
        return self.vectorizer

    def test_model_with_splits(self, model_info, train_test_splits):
        """Test a single model with multiple train/test splits"""
        model_name = model_info['name'].replace('.pkl', '')
        model_path = model_info['path']

        print(f"\nTesting model: {model_name}")

        results = {}

        try:
            # Load the model
            with open(model_path, 'rb') as f:
                model = pickle.load(f)

            for split_ratio in train_test_splits:
                print(f"  Testing with {split_ratio}% training split...")

                try:
                    # Split the data with non-stratified approach if needed
                    try:
                        X_train, X_test, y_train, y_test = train_test_split(
                            self.df['cleaned_comment'],
                            self.df['is_spam'],
                            train_size=split_ratio/100,
                            test_size=1-(split_ratio/100),
                            random_state=42,
                            stratify=self.df['is_spam']
                        )
                    except ValueError:
                        # Fall back to non-stratified split
                        X_train, X_test, y_train, y_test = train_test_split(
                            self.df['cleaned_comment'],
                            self.df['is_spam'],
                            train_size=split_ratio/100,
                            test_size=1-(split_ratio/100),
                            random_state=42
                        )

                    # Vectorize the data
                    if hasattr(self.vectorizer, 'vocabulary_'):
                        # Use existing fitted vectorizer
                        X_train_tfidf = self.vectorizer.transform(X_train)
                        X_test_tfidf = self.vectorizer.transform(X_test)
                    else:
                        # Fit vectorizer on training data
                        X_train_tfidf = self.vectorizer.fit_transform(X_train)
                        X_test_tfidf = self.vectorizer.transform(X_test)

                    # Make predictions
                    try:
                        # Try with sparse matrix first
                        y_pred = model.predict(X_test_tfidf)
                    except:
                        try:
                            # Try with dense matrix
                            y_pred = model.predict(X_test_tfidf.toarray())
                        except Exception as pred_error:
                            print(f"    Error making predictions: {pred_error}")
                            continue

                    # Calculate metrics
                    metrics = self._calculate_metrics(y_test, y_pred)

                    # Store results
                    results[f"split_{split_ratio}"] = {
                        'train_size': len(X_train),
                        'test_size': len(X_test),
                        'train_spam': y_train.sum(),
                        'test_spam': y_test.sum(),
                        'predictions': y_pred.tolist(),
                        'actual': y_test.tolist(),
                        'metrics': metrics
                    }

                    print(f"    Accuracy: {metrics['accuracy']:.4f}, F1: {metrics['f1_score']:.4f}")

                except Exception as e:
                    print(f"    Error in split {split_ratio}%: {e}")
                    results[f"split_{split_ratio}"] = {'error': str(e)}

        except Exception as e:
            print(f"  Error loading model: {e}")
            return {'error': str(e)}

        return results

    def _calculate_metrics(self, y_true, y_pred):
        """Calculate comprehensive performance metrics"""
        metrics = {}

        try:
            metrics['accuracy'] = accuracy_score(y_true, y_pred)
            metrics['precision'] = precision_score(y_true, y_pred, average='weighted', zero_division=0)
            metrics['recall'] = recall_score(y_true, y_pred, average='weighted', zero_division=0)
            metrics['f1_score'] = f1_score(y_true, y_pred, average='weighted', zero_division=0)

            # Confusion matrix
            cm = confusion_matrix(y_true, y_pred)
            metrics['confusion_matrix'] = cm.tolist()

            # Class-specific metrics
            report = classification_report(y_true, y_pred, output_dict=True, zero_division=0)
            metrics['classification_report'] = report

            # ROC AUC if binary classification
            if len(np.unique(y_true)) == 2:
                try:
                    metrics['roc_auc'] = roc_auc_score(y_true, y_pred)
                except:
                    metrics['roc_auc'] = None

        except Exception as e:
            print(f"Error calculating metrics: {e}")
            metrics['error'] = str(e)

        return metrics

    def run_comprehensive_testing(self, train_test_splits=[25, 30, 35]):
        """Run comprehensive testing on working models"""
        print("\n" + "="*60)
        print("RUNNING IMPROVED MODEL TESTING")
        print("="*60)
        print(f"Train/Test splits: {train_test_splits}%")

        # Load and prepare dataset
        self.load_and_prepare_dataset()

        # Load vectorizer
        self.load_vectorizer()

        # Find working models
        working_models = self.find_working_models()

        if not working_models:
            print("No working models found!")
            return

        # Test each working model
        all_results = {}
        successful_tests = 0

        for i, model_info in enumerate(working_models):
            print(f"\nProgress: {i+1}/{len(working_models)}")

            model_results = self.test_model_with_splits(model_info, train_test_splits)
            all_results[model_info['name']] = {
                'model_info': model_info,
                'results': model_results
            }

            # Count successful tests
            if 'error' not in model_results:
                successful_tests += 1

        self.test_results = all_results

        print(f"\nSuccessfully tested {successful_tests}/{len(working_models)} models")

        # Generate summary and save results
        self._generate_summary()
        self._save_results()

        print(f"\n{'='*60}")
        print("IMPROVED TESTING COMPLETED!")
        print(f"Results saved to: {self.output_dir}")
        print(f"{'='*60}")

    def _generate_summary(self):
        """Generate summary of test results"""
        print("\n" + "="*60)
        print("GENERATING SUMMARY")
        print("="*60)

        summary_data = []

        for model_name, model_data in self.test_results.items():
            if 'error' in model_data.get('results', {}):
                continue

            for split_name, split_results in model_data.get('results', {}).items():
                if 'error' in split_results:
                    continue

                metrics = split_results.get('metrics', {})
                if 'error' in metrics:
                    continue

                summary_data.append({
                    'Model': model_name,
                    'Directory': model_data['model_info'].get('directory', ''),
                    'Split': split_name,
                    'Train_Size': split_results.get('train_size', 0),
                    'Test_Size': split_results.get('test_size', 0),
                    'Train_Spam': split_results.get('train_spam', 0),
                    'Test_Spam': split_results.get('test_spam', 0),
                    'Accuracy': metrics.get('accuracy', 0),
                    'Precision': metrics.get('precision', 0),
                    'Recall': metrics.get('recall', 0),
                    'F1_Score': metrics.get('f1_score', 0),
                    'ROC_AUC': metrics.get('roc_auc', 0)
                })

        if summary_data:
            self.summary_df = pd.DataFrame(summary_data)
            print(f"Generated summary for {len(summary_data)} successful test scenarios")

            # Display top performers
            print("\nTop 10 performers by F1-Score:")
            top_performers = self.summary_df.nlargest(10, 'F1_Score')
            print(top_performers[['Model', 'Split', 'Accuracy', 'F1_Score', 'ROC_AUC']].to_string(index=False))
        else:
            print("No valid results to summarize")
            self.summary_df = pd.DataFrame()

    def _save_results(self):
        """Save all results to files"""
        print("\nSaving results...")

        # Create timestamped subdirectory
        results_dir = os.path.join(self.output_dir, f"improved_test_results_{self.timestamp}")
        os.makedirs(results_dir, exist_ok=True)

        # Save raw results as JSON
        results_file = os.path.join(results_dir, f"raw_results_{self.timestamp}.json")
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        print(f"Raw results saved to: {results_file}")

        # Save summary as CSV and Excel
        if not self.summary_df.empty:
            summary_csv = os.path.join(results_dir, f"model_testing_summary_{self.timestamp}.csv")
            summary_excel = os.path.join(results_dir, f"model_testing_summary_{self.timestamp}.xlsx")

            self.summary_df.to_csv(summary_csv, index=False)
            self.summary_df.to_excel(summary_excel, index=False)

            print(f"Summary saved to: {summary_csv}")
            print(f"Summary saved to: {summary_excel}")

        # Generate analysis report
        self._generate_analysis_report(results_dir)

    def _generate_analysis_report(self, results_dir):
        """Generate comprehensive analysis report"""
        report_file = os.path.join(results_dir, f"analysis_report_{self.timestamp}.txt")

        with open(report_file, 'w') as f:
            f.write("YOUTUBE SPAM DETECTION - IMPROVED MODEL TESTING ANALYSIS\n")
            f.write("="*60 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Test Dataset: {self.test_dataset_path}\n")
            f.write(f"Total Models Found: {len(self.test_results)}\n\n")

            if not self.summary_df.empty:
                f.write("DATASET OVERVIEW:\n")
                f.write("-" * 20 + "\n")
                f.write(f"Total Comments: {len(self.df)}\n")
                f.write(f"Spam Comments: {self.df['is_spam'].sum()}\n")
                f.write(f"Ham Comments: {len(self.df) - self.df['is_spam'].sum()}\n")
                f.write(f"Spam Percentage: {(self.df['is_spam'].sum() / len(self.df) * 100):.2f}%\n\n")

                f.write("TOP PERFORMING MODELS:\n")
                f.write("-" * 25 + "\n")
                top_10 = self.summary_df.nlargest(10, 'F1_Score')
                for _, row in top_10.iterrows():
                    f.write(f"{row['Model']} ({row['Split']}): ")
                    f.write(f"Acc={row['Accuracy']:.4f}, F1={row['F1_Score']:.4f}, AUC={row['ROC_AUC']:.4f}\n")

                f.write("\nPERFORMANCE BY SPLIT:\n")
                f.write("-" * 22 + "\n")
                for split in self.summary_df['Split'].unique():
                    split_data = self.summary_df[self.summary_df['Split'] == split]
                    f.write(f"\n{split}:\n")
                    f.write(f"  Average Accuracy: {split_data['Accuracy'].mean():.4f}\n")
                    f.write(f"  Average F1-Score: {split_data['F1_Score'].mean():.4f}\n")
                    f.write(f"  Best F1-Score: {split_data['F1_Score'].max():.4f}\n")
                    f.write(f"  Models tested: {len(split_data)}\n")

        print(f"Analysis report saved to: {report_file}")


def main():
    """Main execution function"""
    print("YOUTUBE SPAM DETECTION - IMPROVED MODEL TESTING")
    print("="*60)

    # Configuration
    test_dataset_path = "../testing_30102024/youtube_comments_i6IOiUi6IYY_123123.xlsx"
    models_base_dir = "../testing_03112024"
    output_dir = "testing"

    # Train/test splits to test
    train_test_splits = [25, 30, 35]

    # Initialize tester
    tester = ImprovedYouTubeSpamTester(
        test_dataset_path=test_dataset_path,
        models_base_dir=models_base_dir,
        output_dir=output_dir
    )

    # Run comprehensive testing
    tester.run_comprehensive_testing(train_test_splits)

    print("\nImproved testing completed successfully!")


if __name__ == "__main__":
    main()
