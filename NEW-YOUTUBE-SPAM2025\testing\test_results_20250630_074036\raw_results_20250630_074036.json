{"NaiveBayes_model_test_size_0.2.pkl": {"model_info": {"name": "NaiveBayes_model_test_size_0.2.pkl", "path": "../testing_03112024/nonoptimz\\NaiveBayes_model_test_size_0.2.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0a'."}}, "NaiveBayes_model_test_size_0.25.pkl": {"model_info": {"name": "NaiveBayes_model_test_size_0.25.pkl", "path": "../testing_03112024/nonoptimz\\NaiveBayes_model_test_size_0.25.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0a'."}}, "NaiveBayes_model_test_size_0.3.pkl": {"model_info": {"name": "NaiveBayes_model_test_size_0.3.pkl", "path": "../testing_03112024/nonoptimz\\NaiveBayes_model_test_size_0.3.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0a'."}}, "NaiveBayes_model_test_size_0.35.pkl": {"model_info": {"name": "NaiveBayes_model_test_size_0.35.pkl", "path": "../testing_03112024/nonoptimz\\NaiveBayes_model_test_size_0.35.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0a'."}}, "RandomForest_model_test_size_0.2.pkl": {"model_info": {"name": "RandomForest_model_test_size_0.2.pkl", "path": "../testing_03112024/nonoptimz\\RandomForest_model_test_size_0.2.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0d'."}}, "RandomForest_model_test_size_0.25.pkl": {"model_info": {"name": "RandomForest_model_test_size_0.25.pkl", "path": "../testing_03112024/nonoptimz\\RandomForest_model_test_size_0.25.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0d'."}}, "RandomForest_model_test_size_0.3.pkl": {"model_info": {"name": "RandomForest_model_test_size_0.3.pkl", "path": "../testing_03112024/nonoptimz\\RandomForest_model_test_size_0.3.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0d'."}}, "RandomForest_model_test_size_0.35.pkl": {"model_info": {"name": "RandomForest_model_test_size_0.35.pkl", "path": "../testing_03112024/nonoptimz\\RandomForest_model_test_size_0.35.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0d'."}}, "SVM_Linear_model_test_size_0.2.pkl": {"model_info": {"name": "SVM_Linear_model_test_size_0.2.pkl", "path": "../testing_03112024/nonoptimz\\SVM_Linear_model_test_size_0.2.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x09'."}}, "SVM_Linear_model_test_size_0.25.pkl": {"model_info": {"name": "SVM_Linear_model_test_size_0.25.pkl", "path": "../testing_03112024/nonoptimz\\SVM_Linear_model_test_size_0.25.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x09'."}}, "SVM_Linear_model_test_size_0.3.pkl": {"model_info": {"name": "SVM_Linear_model_test_size_0.3.pkl", "path": "../testing_03112024/nonoptimz\\SVM_Linear_model_test_size_0.3.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x09'."}}, "SVM_Linear_model_test_size_0.35.pkl": {"model_info": {"name": "SVM_Linear_model_test_size_0.35.pkl", "path": "../testing_03112024/nonoptimz\\SVM_Linear_model_test_size_0.35.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x09'."}}, "SVM_Poly_model_test_size_0.2.pkl": {"model_info": {"name": "SVM_Poly_model_test_size_0.2.pkl", "path": "../testing_03112024/nonoptimz\\SVM_Poly_model_test_size_0.2.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0b'."}}, "SVM_Poly_model_test_size_0.25.pkl": {"model_info": {"name": "SVM_Poly_model_test_size_0.25.pkl", "path": "../testing_03112024/nonoptimz\\SVM_Poly_model_test_size_0.25.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0b'."}}, "SVM_Poly_model_test_size_0.3.pkl": {"model_info": {"name": "SVM_Poly_model_test_size_0.3.pkl", "path": "../testing_03112024/nonoptimz\\SVM_Poly_model_test_size_0.3.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0b'."}}, "SVM_Poly_model_test_size_0.35.pkl": {"model_info": {"name": "SVM_Poly_model_test_size_0.35.pkl", "path": "../testing_03112024/nonoptimz\\SVM_Poly_model_test_size_0.35.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0b'."}}, "SVM_RBF_model_test_size_0.2.pkl": {"model_info": {"name": "SVM_RBF_model_test_size_0.2.pkl", "path": "../testing_03112024/nonoptimz\\SVM_RBF_model_test_size_0.2.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0c'."}}, "SVM_RBF_model_test_size_0.25.pkl": {"model_info": {"name": "SVM_RBF_model_test_size_0.25.pkl", "path": "../testing_03112024/nonoptimz\\SVM_RBF_model_test_size_0.25.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0c'."}}, "SVM_RBF_model_test_size_0.3.pkl": {"model_info": {"name": "SVM_RBF_model_test_size_0.3.pkl", "path": "../testing_03112024/nonoptimz\\SVM_RBF_model_test_size_0.3.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0c'."}}, "SVM_RBF_model_test_size_0.35.pkl": {"model_info": {"name": "SVM_RBF_model_test_size_0.35.pkl", "path": "../testing_03112024/nonoptimz\\SVM_RBF_model_test_size_0.35.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x0c'."}}, "SVM_Sigmoid_model_test_size_0.2.pkl": {"model_info": {"name": "SVM_Sigmoid_model_test_size_0.2.pkl", "path": "../testing_03112024/nonoptimz\\SVM_Sigmoid_model_test_size_0.2.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x08'."}}, "SVM_Sigmoid_model_test_size_0.25.pkl": {"model_info": {"name": "SVM_Sigmoid_model_test_size_0.25.pkl", "path": "../testing_03112024/nonoptimz\\SVM_Sigmoid_model_test_size_0.25.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x08'."}}, "SVM_Sigmoid_model_test_size_0.3.pkl": {"model_info": {"name": "SVM_Sigmoid_model_test_size_0.3.pkl", "path": "../testing_03112024/nonoptimz\\SVM_Sigmoid_model_test_size_0.3.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x08'."}}, "SVM_Sigmoid_model_test_size_0.35.pkl": {"model_info": {"name": "SVM_Sigmoid_model_test_size_0.35.pkl", "path": "../testing_03112024/nonoptimz\\SVM_Sigmoid_model_test_size_0.35.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x08'."}}, "XGBoost_model_test_size_0.2.pkl": {"model_info": {"name": "XGBoost_model_test_size_0.2.pkl", "path": "../testing_03112024/nonoptimz\\XGBoost_model_test_size_0.2.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "XGBoost_model_test_size_0.25.pkl": {"model_info": {"name": "XGBoost_model_test_size_0.25.pkl", "path": "../testing_03112024/nonoptimz\\XGBoost_model_test_size_0.25.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "XGBoost_model_test_size_0.3.pkl": {"model_info": {"name": "XGBoost_model_test_size_0.3.pkl", "path": "../testing_03112024/nonoptimz\\XGBoost_model_test_size_0.3.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "XGBoost_model_test_size_0.35.pkl": {"model_info": {"name": "XGBoost_model_test_size_0.35.pkl", "path": "../testing_03112024/nonoptimz\\XGBoost_model_test_size_0.35.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "Logistic_Regression_model.pkl": {"model_info": {"name": "Logistic_Regression_model.pkl", "path": "../testing_03112024/majorityvote\\Logistic_Regression_model.pkl", "directory": "../testing_03112024/majorityvote"}, "results": {"error": "invalid load key, '\\x03'."}}, "Naive_Bayes_model.pkl": {"model_info": {"name": "Naive_Bayes_model.pkl", "path": "../testing_03112024/majorityvote\\Naive_Bayes_model.pkl", "directory": "../testing_03112024/majorityvote"}, "results": {"error": "invalid load key, '\\x0a'."}}, "Random_Forest_model.pkl": {"model_info": {"name": "Random_Forest_model.pkl", "path": "../testing_03112024/majorityvote\\Random_Forest_model.pkl", "directory": "../testing_03112024/majorityvote"}, "results": {"error": "invalid load key, '\\x0d'."}}, "SVM_(linear_kernel)_model.pkl": {"model_info": {"name": "SVM_(linear_kernel)_model.pkl", "path": "../testing_03112024/majorityvote\\SVM_(linear_kernel)_model.pkl", "directory": "../testing_03112024/majorityvote"}, "results": {"error": "invalid load key, '\\x02'."}}, "SVM_(poly_kernel)_model.pkl": {"model_info": {"name": "SVM_(poly_kernel)_model.pkl", "path": "../testing_03112024/majorityvote\\SVM_(poly_kernel)_model.pkl", "directory": "../testing_03112024/majorityvote"}, "results": {"error": "invalid load key, '\\x04'."}}, "SVM_(rbf_kernel)_model.pkl": {"model_info": {"name": "SVM_(rbf_kernel)_model.pkl", "path": "../testing_03112024/majorityvote\\SVM_(rbf_kernel)_model.pkl", "directory": "../testing_03112024/majorityvote"}, "results": {"error": "invalid load key, '\\x05'."}}, "SVM_(sigmoid_kernel)_model.pkl": {"model_info": {"name": "SVM_(sigmoid_kernel)_model.pkl", "path": "../testing_03112024/majorityvote\\SVM_(sigmoid_kernel)_model.pkl", "directory": "../testing_03112024/majorityvote"}, "results": {"error": "invalid load key, '\\x01'."}}, "XGBoost_model.pkl": {"model_info": {"name": "XGBoost_model.pkl", "path": "../testing_03112024/majorityvote\\XGBoost_model.pkl", "directory": "../testing_03112024/majorityvote"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "GaussianNB_model.pkl": {"model_info": {"name": "GaussianNB_model.pkl", "path": "../testing_03112024/hyperparameter\\GaussianNB_model.pkl", "directory": "../testing_03112024/hyperparameter"}, "results": {"error": "invalid load key, '\\x0a'."}}, "LogisticRegression_model.pkl": {"model_info": {"name": "LogisticRegression_model.pkl", "path": "../testing_03112024/majorityvote\\LogisticRegression_model.pkl", "directory": "../testing_03112024/majorityvote"}, "results": {"error": "invalid load key, '\\x03'."}}, "NaiveBayes_model.pkl": {"model_info": {"name": "NaiveBayes_model.pkl", "path": "../testing_03112024/majorityvote\\NaiveBayes_model.pkl", "directory": "../testing_03112024/majorityvote"}, "results": {"error": "invalid load key, '\\x0a'."}}, "RandomForest_model.pkl": {"model_info": {"name": "RandomForest_model.pkl", "path": "../testing_03112024/majorityvote\\RandomForest_model.pkl", "directory": "../testing_03112024/majorityvote"}, "results": {"error": "invalid load key, '\\x0d'."}}, "SVM_(Linear)_model.pkl": {"model_info": {"name": "SVM_(Linear)_model.pkl", "path": "../testing_03112024/majorityvote\\SVM_(Linear)_model.pkl", "directory": "../testing_03112024/majorityvote"}, "results": {"error": "invalid load key, '\\x02'."}}, "SVM_(Polynomial)_model.pkl": {"model_info": {"name": "SVM_(Polynomial)_model.pkl", "path": "../testing_03112024/majorityvote\\SVM_(Polynomial)_model.pkl", "directory": "../testing_03112024/majorityvote"}, "results": {"error": "invalid load key, '\\x04'."}}, "SVM_(RBF)_model.pkl": {"model_info": {"name": "SVM_(RBF)_model.pkl", "path": "../testing_03112024/majorityvote\\SVM_(RBF)_model.pkl", "directory": "../testing_03112024/majorityvote"}, "results": {"error": "invalid load key, '\\x05'."}}, "SVM_(Sigmoid)_model.pkl": {"model_info": {"name": "SVM_(Sigmoid)_model.pkl", "path": "../testing_03112024/majorityvote\\SVM_(Sigmoid)_model.pkl", "directory": "../testing_03112024/majorityvote"}, "results": {"error": "invalid load key, '\\x01'."}}, "{model_name}_model_test_size_{test_size}.pkl": {"model_info": {"name": "{model_name}_model_test_size_{test_size}.pkl", "path": "../testing_03112024/nonoptimz\\{model_name}_model_test_size_{test_size}.pkl", "directory": "../testing_03112024/nonoptimz"}, "results": {"error": "invalid load key, '\\x08'."}}, "train_25_test_75_REVERSED_adaboost_dt_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_adaboost_dt_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_adaboost_dt_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_adaboost_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_adaboost_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_adaboost_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_dt_best_first_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_dt_best_first_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_dt_best_first_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_dt_entropy_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_dt_entropy_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_dt_entropy_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_dt_entropy_pruned_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_dt_entropy_pruned_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_dt_entropy_pruned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_dt_gini_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_dt_gini_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_dt_gini_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_dt_gini_pruned_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_dt_gini_pruned_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_dt_gini_pruned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_dt_log_loss_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_dt_log_loss_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_dt_log_loss_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_dt_random_split_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_dt_random_split_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_dt_random_split_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_extratrees_large_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_extratrees_large_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_extratrees_large_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_extratrees_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_extratrees_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_extratrees_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_gradientboosting_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_gradientboosting_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_gradientboosting_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_lr_elasticnet_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_lr_elasticnet_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_lr_elasticnet_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_lr_l1_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_lr_l1_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_lr_l1_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_lr_l2_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_lr_l2_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_lr_l2_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_lr_lbfgs_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_lr_lbfgs_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_lr_lbfgs_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_lr_saga_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_lr_saga_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_lr_saga_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_lr_sag_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_lr_sag_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_lr_sag_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_nb_bernoulli_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_nb_bernoulli_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_nb_bernoulli_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_nb_bernoulli_tuned_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_nb_bernoulli_tuned_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_nb_bernoulli_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_nb_complement_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_nb_complement_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_nb_complement_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_nb_multinomial_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_nb_multinomial_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_nb_multinomial_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_nb_multinomial_tuned_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_nb_multinomial_tuned_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_nb_multinomial_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_rf_entropy_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_rf_entropy_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_rf_entropy_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_rf_gini_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_rf_gini_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_rf_gini_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_rf_large_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_rf_large_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_rf_large_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_rf_log_loss_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_rf_log_loss_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_rf_log_loss_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_rf_small_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_rf_small_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_rf_small_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_svm_linear_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_svm_linear_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_svm_linear_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_svm_linear_tuned_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_svm_linear_tuned_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_svm_linear_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_svm_polynomial_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_svm_polynomial_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_svm_polynomial_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_svm_rbf_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_svm_rbf_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_svm_rbf_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_svm_rbf_tuned_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_svm_rbf_tuned_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_svm_rbf_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_25_test_75_REVERSED_svm_sigmoid_model.pkl": {"model_info": {"name": "train_25_test_75_REVERSED_svm_sigmoid_model.pkl", "path": "comprehensive_models\\train_25_test_75_REVERSED_svm_sigmoid_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_adaboost_dt_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_adaboost_dt_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_adaboost_dt_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_adaboost_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_adaboost_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_adaboost_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_dt_best_first_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_dt_best_first_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_dt_best_first_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_dt_entropy_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_dt_entropy_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_dt_entropy_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_dt_entropy_pruned_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_dt_entropy_pruned_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_dt_entropy_pruned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_dt_gini_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_dt_gini_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_dt_gini_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_dt_gini_pruned_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_dt_gini_pruned_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_dt_gini_pruned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_dt_log_loss_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_dt_log_loss_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_dt_log_loss_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_dt_random_split_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_dt_random_split_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_dt_random_split_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_extratrees_large_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_extratrees_large_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_extratrees_large_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_extratrees_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_extratrees_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_extratrees_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_gradientboosting_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_gradientboosting_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_gradientboosting_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_lr_elasticnet_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_lr_elasticnet_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_lr_elasticnet_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_lr_l1_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_lr_l1_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_lr_l1_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_lr_l2_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_lr_l2_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_lr_l2_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_lr_lbfgs_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_lr_lbfgs_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_lr_lbfgs_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_lr_saga_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_lr_saga_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_lr_saga_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_lr_sag_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_lr_sag_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_lr_sag_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_nb_bernoulli_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_nb_bernoulli_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_nb_bernoulli_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_nb_bernoulli_tuned_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_nb_bernoulli_tuned_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_nb_bernoulli_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_nb_complement_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_nb_complement_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_nb_complement_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_nb_multinomial_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_nb_multinomial_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_nb_multinomial_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_nb_multinomial_tuned_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_nb_multinomial_tuned_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_nb_multinomial_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_rf_entropy_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_rf_entropy_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_rf_entropy_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_rf_gini_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_rf_gini_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_rf_gini_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_rf_large_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_rf_large_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_rf_large_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_rf_log_loss_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_rf_log_loss_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_rf_log_loss_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_rf_small_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_rf_small_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_rf_small_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_svm_linear_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_svm_linear_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_svm_linear_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_svm_linear_tuned_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_svm_linear_tuned_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_svm_linear_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_svm_polynomial_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_svm_polynomial_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_svm_polynomial_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_svm_rbf_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_svm_rbf_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_svm_rbf_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_svm_rbf_tuned_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_svm_rbf_tuned_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_svm_rbf_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_30_test_70_REVERSED_svm_sigmoid_model.pkl": {"model_info": {"name": "train_30_test_70_REVERSED_svm_sigmoid_model.pkl", "path": "comprehensive_models\\train_30_test_70_REVERSED_svm_sigmoid_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_adaboost_dt_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_adaboost_dt_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_adaboost_dt_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_adaboost_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_adaboost_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_adaboost_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_dt_best_first_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_dt_best_first_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_dt_best_first_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_dt_entropy_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_dt_entropy_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_dt_entropy_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_dt_entropy_pruned_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_dt_entropy_pruned_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_dt_entropy_pruned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_dt_gini_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_dt_gini_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_dt_gini_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_dt_gini_pruned_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_dt_gini_pruned_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_dt_gini_pruned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_dt_log_loss_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_dt_log_loss_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_dt_log_loss_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_dt_random_split_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_dt_random_split_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_dt_random_split_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_extratrees_large_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_extratrees_large_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_extratrees_large_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_extratrees_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_extratrees_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_extratrees_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_gradientboosting_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_gradientboosting_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_gradientboosting_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_lr_elasticnet_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_lr_elasticnet_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_lr_elasticnet_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_lr_l1_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_lr_l1_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_lr_l1_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_lr_l2_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_lr_l2_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_lr_l2_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_lr_lbfgs_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_lr_lbfgs_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_lr_lbfgs_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_lr_saga_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_lr_saga_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_lr_saga_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_lr_sag_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_lr_sag_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_lr_sag_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_nb_bernoulli_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_nb_bernoulli_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_nb_bernoulli_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_nb_bernoulli_tuned_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_nb_bernoulli_tuned_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_nb_bernoulli_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_nb_complement_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_nb_complement_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_nb_complement_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_nb_multinomial_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_nb_multinomial_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_nb_multinomial_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_nb_multinomial_tuned_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_nb_multinomial_tuned_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_nb_multinomial_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_rf_entropy_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_rf_entropy_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_rf_entropy_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_rf_gini_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_rf_gini_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_rf_gini_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_rf_large_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_rf_large_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_rf_large_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_rf_log_loss_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_rf_log_loss_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_rf_log_loss_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_rf_small_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_rf_small_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_rf_small_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_svm_linear_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_svm_linear_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_svm_linear_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_svm_linear_tuned_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_svm_linear_tuned_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_svm_linear_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_svm_polynomial_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_svm_polynomial_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_svm_polynomial_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_svm_rbf_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_svm_rbf_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_svm_rbf_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_svm_rbf_tuned_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_svm_rbf_tuned_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_svm_rbf_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_35_test_65_REVERSED_svm_sigmoid_model.pkl": {"model_info": {"name": "train_35_test_65_REVERSED_svm_sigmoid_model.pkl", "path": "comprehensive_models\\train_35_test_65_REVERSED_svm_sigmoid_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_adaboost_dt_model.pkl": {"model_info": {"name": "train_65_test_35_adaboost_dt_model.pkl", "path": "comprehensive_models\\train_65_test_35_adaboost_dt_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_adaboost_model.pkl": {"model_info": {"name": "train_65_test_35_adaboost_model.pkl", "path": "comprehensive_models\\train_65_test_35_adaboost_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_dt_best_first_model.pkl": {"model_info": {"name": "train_65_test_35_dt_best_first_model.pkl", "path": "comprehensive_models\\train_65_test_35_dt_best_first_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_dt_entropy_model.pkl": {"model_info": {"name": "train_65_test_35_dt_entropy_model.pkl", "path": "comprehensive_models\\train_65_test_35_dt_entropy_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_dt_entropy_pruned_model.pkl": {"model_info": {"name": "train_65_test_35_dt_entropy_pruned_model.pkl", "path": "comprehensive_models\\train_65_test_35_dt_entropy_pruned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_dt_gini_model.pkl": {"model_info": {"name": "train_65_test_35_dt_gini_model.pkl", "path": "comprehensive_models\\train_65_test_35_dt_gini_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_dt_gini_pruned_model.pkl": {"model_info": {"name": "train_65_test_35_dt_gini_pruned_model.pkl", "path": "comprehensive_models\\train_65_test_35_dt_gini_pruned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_dt_log_loss_model.pkl": {"model_info": {"name": "train_65_test_35_dt_log_loss_model.pkl", "path": "comprehensive_models\\train_65_test_35_dt_log_loss_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_dt_random_split_model.pkl": {"model_info": {"name": "train_65_test_35_dt_random_split_model.pkl", "path": "comprehensive_models\\train_65_test_35_dt_random_split_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_extratrees_large_model.pkl": {"model_info": {"name": "train_65_test_35_extratrees_large_model.pkl", "path": "comprehensive_models\\train_65_test_35_extratrees_large_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_extratrees_model.pkl": {"model_info": {"name": "train_65_test_35_extratrees_model.pkl", "path": "comprehensive_models\\train_65_test_35_extratrees_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_gradientboosting_model.pkl": {"model_info": {"name": "train_65_test_35_gradientboosting_model.pkl", "path": "comprehensive_models\\train_65_test_35_gradientboosting_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_lr_elasticnet_model.pkl": {"model_info": {"name": "train_65_test_35_lr_elasticnet_model.pkl", "path": "comprehensive_models\\train_65_test_35_lr_elasticnet_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_lr_l1_model.pkl": {"model_info": {"name": "train_65_test_35_lr_l1_model.pkl", "path": "comprehensive_models\\train_65_test_35_lr_l1_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_lr_l2_model.pkl": {"model_info": {"name": "train_65_test_35_lr_l2_model.pkl", "path": "comprehensive_models\\train_65_test_35_lr_l2_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_lr_lbfgs_model.pkl": {"model_info": {"name": "train_65_test_35_lr_lbfgs_model.pkl", "path": "comprehensive_models\\train_65_test_35_lr_lbfgs_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_lr_saga_model.pkl": {"model_info": {"name": "train_65_test_35_lr_saga_model.pkl", "path": "comprehensive_models\\train_65_test_35_lr_saga_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_lr_sag_model.pkl": {"model_info": {"name": "train_65_test_35_lr_sag_model.pkl", "path": "comprehensive_models\\train_65_test_35_lr_sag_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_nb_bernoulli_model.pkl": {"model_info": {"name": "train_65_test_35_nb_bernoulli_model.pkl", "path": "comprehensive_models\\train_65_test_35_nb_bernoulli_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_nb_bernoulli_tuned_model.pkl": {"model_info": {"name": "train_65_test_35_nb_bernoulli_tuned_model.pkl", "path": "comprehensive_models\\train_65_test_35_nb_bernoulli_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_nb_complement_model.pkl": {"model_info": {"name": "train_65_test_35_nb_complement_model.pkl", "path": "comprehensive_models\\train_65_test_35_nb_complement_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_nb_multinomial_model.pkl": {"model_info": {"name": "train_65_test_35_nb_multinomial_model.pkl", "path": "comprehensive_models\\train_65_test_35_nb_multinomial_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_nb_multinomial_tuned_model.pkl": {"model_info": {"name": "train_65_test_35_nb_multinomial_tuned_model.pkl", "path": "comprehensive_models\\train_65_test_35_nb_multinomial_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_rf_entropy_model.pkl": {"model_info": {"name": "train_65_test_35_rf_entropy_model.pkl", "path": "comprehensive_models\\train_65_test_35_rf_entropy_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_rf_gini_model.pkl": {"model_info": {"name": "train_65_test_35_rf_gini_model.pkl", "path": "comprehensive_models\\train_65_test_35_rf_gini_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_rf_large_model.pkl": {"model_info": {"name": "train_65_test_35_rf_large_model.pkl", "path": "comprehensive_models\\train_65_test_35_rf_large_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_rf_log_loss_model.pkl": {"model_info": {"name": "train_65_test_35_rf_log_loss_model.pkl", "path": "comprehensive_models\\train_65_test_35_rf_log_loss_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_rf_small_model.pkl": {"model_info": {"name": "train_65_test_35_rf_small_model.pkl", "path": "comprehensive_models\\train_65_test_35_rf_small_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_svm_linear_model.pkl": {"model_info": {"name": "train_65_test_35_svm_linear_model.pkl", "path": "comprehensive_models\\train_65_test_35_svm_linear_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_svm_linear_tuned_model.pkl": {"model_info": {"name": "train_65_test_35_svm_linear_tuned_model.pkl", "path": "comprehensive_models\\train_65_test_35_svm_linear_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_svm_polynomial_model.pkl": {"model_info": {"name": "train_65_test_35_svm_polynomial_model.pkl", "path": "comprehensive_models\\train_65_test_35_svm_polynomial_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_svm_rbf_model.pkl": {"model_info": {"name": "train_65_test_35_svm_rbf_model.pkl", "path": "comprehensive_models\\train_65_test_35_svm_rbf_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_svm_rbf_tuned_model.pkl": {"model_info": {"name": "train_65_test_35_svm_rbf_tuned_model.pkl", "path": "comprehensive_models\\train_65_test_35_svm_rbf_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_svm_sigmoid_model.pkl": {"model_info": {"name": "train_65_test_35_svm_sigmoid_model.pkl", "path": "comprehensive_models\\train_65_test_35_svm_sigmoid_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_adaboost_dt_model.pkl": {"model_info": {"name": "train_70_test_30_adaboost_dt_model.pkl", "path": "comprehensive_models\\train_70_test_30_adaboost_dt_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_adaboost_model.pkl": {"model_info": {"name": "train_70_test_30_adaboost_model.pkl", "path": "comprehensive_models\\train_70_test_30_adaboost_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_dt_best_first_model.pkl": {"model_info": {"name": "train_70_test_30_dt_best_first_model.pkl", "path": "comprehensive_models\\train_70_test_30_dt_best_first_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_dt_entropy_model.pkl": {"model_info": {"name": "train_70_test_30_dt_entropy_model.pkl", "path": "comprehensive_models\\train_70_test_30_dt_entropy_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_dt_entropy_pruned_model.pkl": {"model_info": {"name": "train_70_test_30_dt_entropy_pruned_model.pkl", "path": "comprehensive_models\\train_70_test_30_dt_entropy_pruned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_dt_gini_model.pkl": {"model_info": {"name": "train_70_test_30_dt_gini_model.pkl", "path": "comprehensive_models\\train_70_test_30_dt_gini_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_dt_gini_pruned_model.pkl": {"model_info": {"name": "train_70_test_30_dt_gini_pruned_model.pkl", "path": "comprehensive_models\\train_70_test_30_dt_gini_pruned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_dt_log_loss_model.pkl": {"model_info": {"name": "train_70_test_30_dt_log_loss_model.pkl", "path": "comprehensive_models\\train_70_test_30_dt_log_loss_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_dt_random_split_model.pkl": {"model_info": {"name": "train_70_test_30_dt_random_split_model.pkl", "path": "comprehensive_models\\train_70_test_30_dt_random_split_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_extratrees_large_model.pkl": {"model_info": {"name": "train_70_test_30_extratrees_large_model.pkl", "path": "comprehensive_models\\train_70_test_30_extratrees_large_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_extratrees_model.pkl": {"model_info": {"name": "train_70_test_30_extratrees_model.pkl", "path": "comprehensive_models\\train_70_test_30_extratrees_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_gradientboosting_model.pkl": {"model_info": {"name": "train_70_test_30_gradientboosting_model.pkl", "path": "comprehensive_models\\train_70_test_30_gradientboosting_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_lr_elasticnet_model.pkl": {"model_info": {"name": "train_70_test_30_lr_elasticnet_model.pkl", "path": "comprehensive_models\\train_70_test_30_lr_elasticnet_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_lr_l1_model.pkl": {"model_info": {"name": "train_70_test_30_lr_l1_model.pkl", "path": "comprehensive_models\\train_70_test_30_lr_l1_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_lr_l2_model.pkl": {"model_info": {"name": "train_70_test_30_lr_l2_model.pkl", "path": "comprehensive_models\\train_70_test_30_lr_l2_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_lr_lbfgs_model.pkl": {"model_info": {"name": "train_70_test_30_lr_lbfgs_model.pkl", "path": "comprehensive_models\\train_70_test_30_lr_lbfgs_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_lr_saga_model.pkl": {"model_info": {"name": "train_70_test_30_lr_saga_model.pkl", "path": "comprehensive_models\\train_70_test_30_lr_saga_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_lr_sag_model.pkl": {"model_info": {"name": "train_70_test_30_lr_sag_model.pkl", "path": "comprehensive_models\\train_70_test_30_lr_sag_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_nb_bernoulli_model.pkl": {"model_info": {"name": "train_70_test_30_nb_bernoulli_model.pkl", "path": "comprehensive_models\\train_70_test_30_nb_bernoulli_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_nb_bernoulli_tuned_model.pkl": {"model_info": {"name": "train_70_test_30_nb_bernoulli_tuned_model.pkl", "path": "comprehensive_models\\train_70_test_30_nb_bernoulli_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_nb_complement_model.pkl": {"model_info": {"name": "train_70_test_30_nb_complement_model.pkl", "path": "comprehensive_models\\train_70_test_30_nb_complement_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_nb_multinomial_model.pkl": {"model_info": {"name": "train_70_test_30_nb_multinomial_model.pkl", "path": "comprehensive_models\\train_70_test_30_nb_multinomial_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_nb_multinomial_tuned_model.pkl": {"model_info": {"name": "train_70_test_30_nb_multinomial_tuned_model.pkl", "path": "comprehensive_models\\train_70_test_30_nb_multinomial_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_rf_entropy_model.pkl": {"model_info": {"name": "train_70_test_30_rf_entropy_model.pkl", "path": "comprehensive_models\\train_70_test_30_rf_entropy_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_rf_gini_model.pkl": {"model_info": {"name": "train_70_test_30_rf_gini_model.pkl", "path": "comprehensive_models\\train_70_test_30_rf_gini_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_rf_large_model.pkl": {"model_info": {"name": "train_70_test_30_rf_large_model.pkl", "path": "comprehensive_models\\train_70_test_30_rf_large_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_rf_log_loss_model.pkl": {"model_info": {"name": "train_70_test_30_rf_log_loss_model.pkl", "path": "comprehensive_models\\train_70_test_30_rf_log_loss_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_rf_small_model.pkl": {"model_info": {"name": "train_70_test_30_rf_small_model.pkl", "path": "comprehensive_models\\train_70_test_30_rf_small_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_svm_linear_model.pkl": {"model_info": {"name": "train_70_test_30_svm_linear_model.pkl", "path": "comprehensive_models\\train_70_test_30_svm_linear_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_svm_linear_tuned_model.pkl": {"model_info": {"name": "train_70_test_30_svm_linear_tuned_model.pkl", "path": "comprehensive_models\\train_70_test_30_svm_linear_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_svm_polynomial_model.pkl": {"model_info": {"name": "train_70_test_30_svm_polynomial_model.pkl", "path": "comprehensive_models\\train_70_test_30_svm_polynomial_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_svm_rbf_model.pkl": {"model_info": {"name": "train_70_test_30_svm_rbf_model.pkl", "path": "comprehensive_models\\train_70_test_30_svm_rbf_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_svm_rbf_tuned_model.pkl": {"model_info": {"name": "train_70_test_30_svm_rbf_tuned_model.pkl", "path": "comprehensive_models\\train_70_test_30_svm_rbf_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_70_test_30_svm_sigmoid_model.pkl": {"model_info": {"name": "train_70_test_30_svm_sigmoid_model.pkl", "path": "comprehensive_models\\train_70_test_30_svm_sigmoid_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_adaboost_dt_model.pkl": {"model_info": {"name": "train_75_test_25_adaboost_dt_model.pkl", "path": "comprehensive_models\\train_75_test_25_adaboost_dt_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_adaboost_model.pkl": {"model_info": {"name": "train_75_test_25_adaboost_model.pkl", "path": "comprehensive_models\\train_75_test_25_adaboost_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_dt_best_first_model.pkl": {"model_info": {"name": "train_75_test_25_dt_best_first_model.pkl", "path": "comprehensive_models\\train_75_test_25_dt_best_first_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_dt_entropy_model.pkl": {"model_info": {"name": "train_75_test_25_dt_entropy_model.pkl", "path": "comprehensive_models\\train_75_test_25_dt_entropy_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_dt_entropy_pruned_model.pkl": {"model_info": {"name": "train_75_test_25_dt_entropy_pruned_model.pkl", "path": "comprehensive_models\\train_75_test_25_dt_entropy_pruned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_dt_gini_model.pkl": {"model_info": {"name": "train_75_test_25_dt_gini_model.pkl", "path": "comprehensive_models\\train_75_test_25_dt_gini_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_dt_gini_pruned_model.pkl": {"model_info": {"name": "train_75_test_25_dt_gini_pruned_model.pkl", "path": "comprehensive_models\\train_75_test_25_dt_gini_pruned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_dt_log_loss_model.pkl": {"model_info": {"name": "train_75_test_25_dt_log_loss_model.pkl", "path": "comprehensive_models\\train_75_test_25_dt_log_loss_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_dt_random_split_model.pkl": {"model_info": {"name": "train_75_test_25_dt_random_split_model.pkl", "path": "comprehensive_models\\train_75_test_25_dt_random_split_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_extratrees_large_model.pkl": {"model_info": {"name": "train_75_test_25_extratrees_large_model.pkl", "path": "comprehensive_models\\train_75_test_25_extratrees_large_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_extratrees_model.pkl": {"model_info": {"name": "train_75_test_25_extratrees_model.pkl", "path": "comprehensive_models\\train_75_test_25_extratrees_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_gradientboosting_model.pkl": {"model_info": {"name": "train_75_test_25_gradientboosting_model.pkl", "path": "comprehensive_models\\train_75_test_25_gradientboosting_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_lr_elasticnet_model.pkl": {"model_info": {"name": "train_75_test_25_lr_elasticnet_model.pkl", "path": "comprehensive_models\\train_75_test_25_lr_elasticnet_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_lr_l1_model.pkl": {"model_info": {"name": "train_75_test_25_lr_l1_model.pkl", "path": "comprehensive_models\\train_75_test_25_lr_l1_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_lr_l2_model.pkl": {"model_info": {"name": "train_75_test_25_lr_l2_model.pkl", "path": "comprehensive_models\\train_75_test_25_lr_l2_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_lr_lbfgs_model.pkl": {"model_info": {"name": "train_75_test_25_lr_lbfgs_model.pkl", "path": "comprehensive_models\\train_75_test_25_lr_lbfgs_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_lr_saga_model.pkl": {"model_info": {"name": "train_75_test_25_lr_saga_model.pkl", "path": "comprehensive_models\\train_75_test_25_lr_saga_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_lr_sag_model.pkl": {"model_info": {"name": "train_75_test_25_lr_sag_model.pkl", "path": "comprehensive_models\\train_75_test_25_lr_sag_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_nb_bernoulli_model.pkl": {"model_info": {"name": "train_75_test_25_nb_bernoulli_model.pkl", "path": "comprehensive_models\\train_75_test_25_nb_bernoulli_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_nb_bernoulli_tuned_model.pkl": {"model_info": {"name": "train_75_test_25_nb_bernoulli_tuned_model.pkl", "path": "comprehensive_models\\train_75_test_25_nb_bernoulli_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_nb_complement_model.pkl": {"model_info": {"name": "train_75_test_25_nb_complement_model.pkl", "path": "comprehensive_models\\train_75_test_25_nb_complement_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_nb_multinomial_model.pkl": {"model_info": {"name": "train_75_test_25_nb_multinomial_model.pkl", "path": "comprehensive_models\\train_75_test_25_nb_multinomial_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_nb_multinomial_tuned_model.pkl": {"model_info": {"name": "train_75_test_25_nb_multinomial_tuned_model.pkl", "path": "comprehensive_models\\train_75_test_25_nb_multinomial_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_rf_entropy_model.pkl": {"model_info": {"name": "train_75_test_25_rf_entropy_model.pkl", "path": "comprehensive_models\\train_75_test_25_rf_entropy_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_rf_gini_model.pkl": {"model_info": {"name": "train_75_test_25_rf_gini_model.pkl", "path": "comprehensive_models\\train_75_test_25_rf_gini_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_rf_large_model.pkl": {"model_info": {"name": "train_75_test_25_rf_large_model.pkl", "path": "comprehensive_models\\train_75_test_25_rf_large_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_rf_log_loss_model.pkl": {"model_info": {"name": "train_75_test_25_rf_log_loss_model.pkl", "path": "comprehensive_models\\train_75_test_25_rf_log_loss_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_rf_small_model.pkl": {"model_info": {"name": "train_75_test_25_rf_small_model.pkl", "path": "comprehensive_models\\train_75_test_25_rf_small_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_svm_linear_model.pkl": {"model_info": {"name": "train_75_test_25_svm_linear_model.pkl", "path": "comprehensive_models\\train_75_test_25_svm_linear_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_svm_linear_tuned_model.pkl": {"model_info": {"name": "train_75_test_25_svm_linear_tuned_model.pkl", "path": "comprehensive_models\\train_75_test_25_svm_linear_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_svm_polynomial_model.pkl": {"model_info": {"name": "train_75_test_25_svm_polynomial_model.pkl", "path": "comprehensive_models\\train_75_test_25_svm_polynomial_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_svm_rbf_model.pkl": {"model_info": {"name": "train_75_test_25_svm_rbf_model.pkl", "path": "comprehensive_models\\train_75_test_25_svm_rbf_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_svm_rbf_tuned_model.pkl": {"model_info": {"name": "train_75_test_25_svm_rbf_tuned_model.pkl", "path": "comprehensive_models\\train_75_test_25_svm_rbf_tuned_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_75_test_25_svm_sigmoid_model.pkl": {"model_info": {"name": "train_75_test_25_svm_sigmoid_model.pkl", "path": "comprehensive_models\\train_75_test_25_svm_sigmoid_model.pkl", "directory": "comprehensive_models"}, "results": {"error": "invalid load key, '\\x0e'."}}, "train_65_test_35_logistic_regression_model.pkl": {"model_info": {"name": "train_65_test_35_logistic_regression_model.pkl", "path": "dataset\\train_65_test_35_logistic_regression_model.pkl", "directory": "dataset"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "train_65_test_35_naive_bayes_model.pkl": {"model_info": {"name": "train_65_test_35_naive_bayes_model.pkl", "path": "dataset\\train_65_test_35_naive_bayes_model.pkl", "directory": "dataset"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "train_65_test_35_random_forest_model.pkl": {"model_info": {"name": "train_65_test_35_random_forest_model.pkl", "path": "dataset\\train_65_test_35_random_forest_model.pkl", "directory": "dataset"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "train_65_test_35_svm_model.pkl": {"model_info": {"name": "train_65_test_35_svm_model.pkl", "path": "dataset\\train_65_test_35_svm_model.pkl", "directory": "dataset"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "train_70_test_30_logistic_regression_model.pkl": {"model_info": {"name": "train_70_test_30_logistic_regression_model.pkl", "path": "dataset\\train_70_test_30_logistic_regression_model.pkl", "directory": "dataset"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "train_70_test_30_naive_bayes_model.pkl": {"model_info": {"name": "train_70_test_30_naive_bayes_model.pkl", "path": "dataset\\train_70_test_30_naive_bayes_model.pkl", "directory": "dataset"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "train_70_test_30_random_forest_model.pkl": {"model_info": {"name": "train_70_test_30_random_forest_model.pkl", "path": "dataset\\train_70_test_30_random_forest_model.pkl", "directory": "dataset"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "train_70_test_30_svm_model.pkl": {"model_info": {"name": "train_70_test_30_svm_model.pkl", "path": "dataset\\train_70_test_30_svm_model.pkl", "directory": "dataset"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "train_75_test_25_logistic_regression_model.pkl": {"model_info": {"name": "train_75_test_25_logistic_regression_model.pkl", "path": "dataset\\train_75_test_25_logistic_regression_model.pkl", "directory": "dataset"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "train_75_test_25_naive_bayes_model.pkl": {"model_info": {"name": "train_75_test_25_naive_bayes_model.pkl", "path": "dataset\\train_75_test_25_naive_bayes_model.pkl", "directory": "dataset"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "train_75_test_25_random_forest_model.pkl": {"model_info": {"name": "train_75_test_25_random_forest_model.pkl", "path": "dataset\\train_75_test_25_random_forest_model.pkl", "directory": "dataset"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}, "train_75_test_25_svm_model.pkl": {"model_info": {"name": "train_75_test_25_svm_model.pkl", "path": "dataset\\train_75_test_25_svm_model.pkl", "directory": "dataset"}, "results": {"error": "The least populated class in y has only 1 member, which is too few. The minimum number of groups for any class cannot be less than 2."}}}