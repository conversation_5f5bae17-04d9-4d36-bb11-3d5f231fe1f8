{"XGBoost_model_test_size_0.2.pkl": {"model_info": {"name": "XGBoost_model_test_size_0.2.pkl", "path": "../testing_03112024\\nonoptimz\\XGBoost_model_test_size_0.2.pkl", "directory": "nonoptimz"}, "results": {}}, "XGBoost_model_test_size_0.25.pkl": {"model_info": {"name": "XGBoost_model_test_size_0.25.pkl", "path": "../testing_03112024\\nonoptimz\\XGBoost_model_test_size_0.25.pkl", "directory": "nonoptimz"}, "results": {}}, "XGBoost_model_test_size_0.3.pkl": {"model_info": {"name": "XGBoost_model_test_size_0.3.pkl", "path": "../testing_03112024\\nonoptimz\\XGBoost_model_test_size_0.3.pkl", "directory": "nonoptimz"}, "results": {}}, "XGBoost_model_test_size_0.35.pkl": {"model_info": {"name": "XGBoost_model_test_size_0.35.pkl", "path": "../testing_03112024\\nonoptimz\\XGBoost_model_test_size_0.35.pkl", "directory": "nonoptimz"}, "results": {}}, "XGBoost_model.pkl": {"model_info": {"name": "XGBoost_model.pkl", "path": "../testing_03112024\\majorityvote\\XGBoost_model.pkl", "directory": "majorityvote"}, "results": {}}}