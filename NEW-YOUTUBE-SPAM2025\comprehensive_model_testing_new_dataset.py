#!/usr/bin/env python3
"""
Comprehensive ML Model Testing for YouTube Spam Detection
Testing existing trained models on new YouTube comments dataset
with multiple train/test split scenarios (25%, 30%, 35%)

Author: AI Assistant
Date: 2025-06-30
"""

import pandas as pd
import numpy as np
import pickle
import joblib
import os
import json
import warnings
from datetime import datetime
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns

# ML Libraries
from sklearn.model_selection import train_test_split
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report, roc_auc_score
)
from sklearn.preprocessing import LabelEncoder

# Text preprocessing
import re
import string
from nltk.corpus import stopwords
from nltk.stem import PorterStemmer
import nltk

# Download required NLTK data
try:
    nltk.download('stopwords', quiet=True)
except:
    pass

warnings.filterwarnings('ignore')

class YouTubeSpamModelTester:
    """Comprehensive testing of existing ML models on new YouTube comments dataset"""
    
    def __init__(self, test_dataset_path, models_base_dir, output_dir):
        """
        Initialize the model tester
        
        Args:
            test_dataset_path: Path to the new YouTube comments dataset
            models_base_dir: Base directory containing trained models
            output_dir: Directory to save test results
        """
        self.test_dataset_path = test_dataset_path
        self.models_base_dir = models_base_dir
        self.output_dir = output_dir
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Initialize components
        self.stemmer = PorterStemmer()
        self.stop_words = set(stopwords.words('english'))
        self.vectorizer = None
        self.label_encoder = LabelEncoder()
        
        # Results storage
        self.test_results = {}
        self.model_predictions = {}
        self.performance_metrics = {}
        
        print(f"YouTubeSpamModelTester initialized")
        print(f"Test dataset: {test_dataset_path}")
        print(f"Models directory: {models_base_dir}")
        print(f"Output directory: {output_dir}")
        print(f"Timestamp: {self.timestamp}")
    
    def clean_text(self, text):
        """Clean and preprocess text data"""
        if pd.isna(text):
            return ""
        
        # Convert to string and lowercase
        text = str(text).lower()
        
        # Remove URLs
        text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
        
        # Remove user mentions and hashtags
        text = re.sub(r'@\w+|#\w+', '', text)
        
        # Remove punctuation and numbers
        text = re.sub(r'[^a-zA-Z\s]', '', text)
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        # Remove stopwords and stem
        words = text.split()
        words = [self.stemmer.stem(word) for word in words if word not in self.stop_words and len(word) > 2]
        
        return ' '.join(words)
    
    def load_and_prepare_dataset(self):
        """Load and prepare the new YouTube comments dataset"""
        print("\n" + "="*60)
        print("LOADING AND PREPARING DATASET")
        print("="*60)
        
        # Load dataset - try CSV first, then Excel
        try:
            self.df = pd.read_csv(self.test_dataset_path)
        except:
            try:
                self.df = pd.read_excel(self.test_dataset_path)
            except:
                raise ValueError(f"Could not read dataset from {self.test_dataset_path}")
        
        print(f"Original dataset shape: {self.df.shape}")
        print(f"Columns: {list(self.df.columns)}")
        
        # Check if we have the required columns
        if 'Comment' not in self.df.columns:
            raise ValueError("Dataset must have 'Comment' column")
        
        # Create labels based on comment characteristics (rule-based labeling)
        self.df['cleaned_comment'] = self.df['Comment'].apply(self.clean_text)
        
        # Rule-based spam detection for labeling
        self.df['is_spam'] = self.df['Comment'].apply(self._detect_spam_patterns)
        
        # Remove empty comments
        self.df = self.df[self.df['cleaned_comment'].str.len() > 0].copy()
        
        print(f"After cleaning: {self.df.shape}")
        print(f"Spam distribution:\n{self.df['is_spam'].value_counts()}")
        
        return self.df
    
    def _detect_spam_patterns(self, comment):
        """Rule-based spam detection for labeling"""
        if pd.isna(comment):
            return 0
        
        comment_lower = str(comment).lower()
        
        # Spam indicators
        spam_patterns = [
            r'check out my',
            r'subscribe to my',
            r'visit my channel',
            r'click here',
            r'free money',
            r'make money',
            r'earn \$',
            r'work from home',
            r'amazing opportunity',
            r'limited time',
            r'act now',
            r'call now',
            r'www\.',
            r'http',
            r'\.com',
            r'follow me',
            r'dm me',
            r'text me',
            r'whatsapp',
            r'telegram'
        ]
        
        # Check for spam patterns
        spam_score = 0
        for pattern in spam_patterns:
            if re.search(pattern, comment_lower):
                spam_score += 1
        
        # Additional checks
        if len(comment) > 500:  # Very long comments
            spam_score += 1
        
        if comment.count('!') > 3:  # Excessive exclamation marks
            spam_score += 1
        
        if len(re.findall(r'[A-Z]{3,}', comment)) > 2:  # Excessive caps
            spam_score += 1
        
        # Return 1 for spam if score >= 2, else 0 for ham
        return 1 if spam_score >= 2 else 0
    
    def find_available_models(self):
        """Find all available trained models"""
        print("\n" + "="*60)
        print("FINDING AVAILABLE MODELS")
        print("="*60)
        
        model_files = []
        
        # Search in multiple directories
        search_dirs = [
            self.models_base_dir,
            "../testing_03112024",
            "../testing_03112024/cv",
            "../testing_03112024/awal",
            "../testing_03112024/crossval",
            "../testing_03112024/hyperparameter",
            "../testing_03112024/majorityvote",
            "../testing_03112024/nonoptimz",
            "comprehensive_models",
            "dataset"
        ]
        
        for search_dir in search_dirs:
            if os.path.exists(search_dir):
                for file in os.listdir(search_dir):
                    if file.endswith('.pkl') and 'model' in file.lower():
                        model_path = os.path.join(search_dir, file)
                        model_files.append({
                            'name': file,
                            'path': model_path,
                            'directory': search_dir
                        })
        
        print(f"Found {len(model_files)} model files:")
        for model in model_files[:10]:  # Show first 10
            print(f"  - {model['name']} ({model['directory']})")
        
        if len(model_files) > 10:
            print(f"  ... and {len(model_files) - 10} more models")
        
        return model_files
    
    def load_vectorizer(self):
        """Load or create TF-IDF vectorizer"""
        vectorizer_paths = [
            "../testing_03112024/tfidf_vectorizer.pkl",
            "../testing_03112024/cv/tfidf_vectorizer.pkl",
            "../testing_03112024/nonoptimz/tfidf_vectorizer.pkl",
            "../tfidf_vectorizer.pkl"
        ]
        
        for path in vectorizer_paths:
            if os.path.exists(path):
                try:
                    with open(path, 'rb') as f:
                        self.vectorizer = pickle.load(f)
                    print(f"Loaded existing vectorizer from: {path}")
                    return self.vectorizer
                except Exception as e:
                    print(f"Error loading vectorizer from {path}: {e}")
                    continue
        
        # Create new vectorizer if none found
        print("Creating new TF-IDF vectorizer...")
        self.vectorizer = TfidfVectorizer(
            max_features=5000,
            stop_words='english',
            ngram_range=(1, 2),
            min_df=2,
            max_df=0.95
        )
        
        return self.vectorizer

    def test_model_on_splits(self, model_info, train_test_splits):
        """Test a single model on multiple train/test splits"""
        model_name = model_info['name'].replace('.pkl', '')
        model_path = model_info['path']

        print(f"\nTesting model: {model_name}")

        results = {}

        try:
            # Load the model
            with open(model_path, 'rb') as f:
                model = pickle.load(f)

            for split_ratio in train_test_splits:
                print(f"  Testing with {split_ratio}% training split...")

                # Split the data
                X_train, X_test, y_train, y_test = train_test_split(
                    self.df['cleaned_comment'],
                    self.df['is_spam'],
                    train_size=split_ratio/100,
                    test_size=1-(split_ratio/100),
                    random_state=42,
                    stratify=self.df['is_spam']
                )

                # Fit vectorizer on training data or use existing
                if hasattr(self.vectorizer, 'vocabulary_'):
                    # Use existing fitted vectorizer
                    X_train_tfidf = self.vectorizer.transform(X_train)
                    X_test_tfidf = self.vectorizer.transform(X_test)
                else:
                    # Fit vectorizer on training data
                    X_train_tfidf = self.vectorizer.fit_transform(X_train)
                    X_test_tfidf = self.vectorizer.transform(X_test)

                # Make predictions
                try:
                    if hasattr(X_train_tfidf, 'toarray'):
                        y_pred = model.predict(X_test_tfidf.toarray())
                    else:
                        y_pred = model.predict(X_test_tfidf)

                    # Calculate metrics
                    metrics = self._calculate_metrics(y_test, y_pred)

                    # Store results
                    results[f"split_{split_ratio}"] = {
                        'train_size': len(X_train),
                        'test_size': len(X_test),
                        'predictions': y_pred.tolist(),
                        'actual': y_test.tolist(),
                        'metrics': metrics
                    }

                    print(f"    Accuracy: {metrics['accuracy']:.4f}")

                except Exception as e:
                    print(f"    Error making predictions: {e}")
                    results[f"split_{split_ratio}"] = {'error': str(e)}

        except Exception as e:
            print(f"  Error loading model: {e}")
            return {'error': str(e)}

        return results

    def _calculate_metrics(self, y_true, y_pred):
        """Calculate comprehensive performance metrics"""
        metrics = {}

        try:
            metrics['accuracy'] = accuracy_score(y_true, y_pred)
            metrics['precision'] = precision_score(y_true, y_pred, average='weighted', zero_division=0)
            metrics['recall'] = recall_score(y_true, y_pred, average='weighted', zero_division=0)
            metrics['f1_score'] = f1_score(y_true, y_pred, average='weighted', zero_division=0)

            # Confusion matrix
            cm = confusion_matrix(y_true, y_pred)
            metrics['confusion_matrix'] = cm.tolist()

            # Class-specific metrics
            report = classification_report(y_true, y_pred, output_dict=True, zero_division=0)
            metrics['classification_report'] = report

            # ROC AUC if binary classification
            if len(np.unique(y_true)) == 2:
                try:
                    metrics['roc_auc'] = roc_auc_score(y_true, y_pred)
                except:
                    metrics['roc_auc'] = None

        except Exception as e:
            print(f"Error calculating metrics: {e}")
            metrics['error'] = str(e)

        return metrics

    def run_comprehensive_testing(self, train_test_splits=[25, 30, 35]):
        """Run comprehensive testing on all available models"""
        print("\n" + "="*60)
        print("RUNNING COMPREHENSIVE MODEL TESTING")
        print("="*60)
        print(f"Train/Test splits: {train_test_splits}%")

        # Load and prepare dataset
        self.load_and_prepare_dataset()

        # Load vectorizer
        self.load_vectorizer()

        # Find available models
        model_files = self.find_available_models()

        if not model_files:
            print("No model files found!")
            return

        # Test each model
        all_results = {}

        for i, model_info in enumerate(model_files):
            print(f"\nProgress: {i+1}/{len(model_files)}")

            model_results = self.test_model_on_splits(model_info, train_test_splits)
            all_results[model_info['name']] = {
                'model_info': model_info,
                'results': model_results
            }

        self.test_results = all_results

        # Generate summary
        self._generate_summary()

        # Save results
        self._save_results()

        print(f"\n{'='*60}")
        print("TESTING COMPLETED!")
        print(f"Results saved to: {self.output_dir}")
        print(f"{'='*60}")

    def _generate_summary(self):
        """Generate summary of all test results"""
        print("\n" + "="*60)
        print("GENERATING SUMMARY")
        print("="*60)

        summary_data = []

        for model_name, model_data in self.test_results.items():
            if 'error' in model_data.get('results', {}):
                continue

            for split_name, split_results in model_data.get('results', {}).items():
                if 'error' in split_results:
                    continue

                metrics = split_results.get('metrics', {})
                if 'error' in metrics:
                    continue

                summary_data.append({
                    'Model': model_name,
                    'Split': split_name,
                    'Train_Size': split_results.get('train_size', 0),
                    'Test_Size': split_results.get('test_size', 0),
                    'Accuracy': metrics.get('accuracy', 0),
                    'Precision': metrics.get('precision', 0),
                    'Recall': metrics.get('recall', 0),
                    'F1_Score': metrics.get('f1_score', 0),
                    'ROC_AUC': metrics.get('roc_auc', 0)
                })

        if summary_data:
            self.summary_df = pd.DataFrame(summary_data)
            print(f"Generated summary for {len(summary_data)} test scenarios")

            # Display top performers
            print("\nTop 10 performers by Accuracy:")
            top_performers = self.summary_df.nlargest(10, 'Accuracy')
            print(top_performers[['Model', 'Split', 'Accuracy', 'F1_Score']].to_string(index=False))
        else:
            print("No valid results to summarize")
            self.summary_df = pd.DataFrame()

    def _save_results(self):
        """Save all results to files"""
        print("\nSaving results...")

        # Create timestamped subdirectory
        results_dir = os.path.join(self.output_dir, f"test_results_{self.timestamp}")
        os.makedirs(results_dir, exist_ok=True)

        # Save raw results as JSON
        results_file = os.path.join(results_dir, f"raw_results_{self.timestamp}.json")
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        print(f"Raw results saved to: {results_file}")

        # Save summary as CSV and Excel
        if not self.summary_df.empty:
            summary_csv = os.path.join(results_dir, f"model_testing_summary_{self.timestamp}.csv")
            summary_excel = os.path.join(results_dir, f"model_testing_summary_{self.timestamp}.xlsx")

            self.summary_df.to_csv(summary_csv, index=False)
            self.summary_df.to_excel(summary_excel, index=False)

            print(f"Summary saved to: {summary_csv}")
            print(f"Summary saved to: {summary_excel}")

        # Save detailed predictions
        self._save_detailed_predictions(results_dir)

        # Generate analysis report
        self._generate_analysis_report(results_dir)

    def _save_detailed_predictions(self, results_dir):
        """Save detailed predictions for each model and split"""
        predictions_data = []

        for model_name, model_data in self.test_results.items():
            if 'error' in model_data.get('results', {}):
                continue

            for split_name, split_results in model_data.get('results', {}).items():
                if 'error' in split_results:
                    continue

                predictions = split_results.get('predictions', [])
                actual = split_results.get('actual', [])

                for i, (pred, true) in enumerate(zip(predictions, actual)):
                    predictions_data.append({
                        'Model': model_name,
                        'Split': split_name,
                        'Index': i,
                        'Predicted': pred,
                        'Actual': true,
                        'Correct': pred == true
                    })

        if predictions_data:
            predictions_df = pd.DataFrame(predictions_data)
            predictions_file = os.path.join(results_dir, f"detailed_predictions_{self.timestamp}.csv")
            predictions_df.to_csv(predictions_file, index=False)
            print(f"Detailed predictions saved to: {predictions_file}")

    def _generate_analysis_report(self, results_dir):
        """Generate comprehensive analysis report"""
        report_file = os.path.join(results_dir, f"analysis_report_{self.timestamp}.txt")

        with open(report_file, 'w') as f:
            f.write("YOUTUBE SPAM DETECTION - MODEL TESTING ANALYSIS REPORT\n")
            f.write("="*60 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Test Dataset: {self.test_dataset_path}\n")
            f.write(f"Total Models Tested: {len(self.test_results)}\n\n")

            if not self.summary_df.empty:
                f.write("DATASET OVERVIEW:\n")
                f.write("-" * 20 + "\n")
                f.write(f"Total Comments: {len(self.df)}\n")
                f.write(f"Spam Comments: {self.df['is_spam'].sum()}\n")
                f.write(f"Ham Comments: {len(self.df) - self.df['is_spam'].sum()}\n")
                f.write(f"Spam Percentage: {(self.df['is_spam'].sum() / len(self.df) * 100):.2f}%\n\n")

                f.write("TOP PERFORMING MODELS:\n")
                f.write("-" * 25 + "\n")
                top_10 = self.summary_df.nlargest(10, 'Accuracy')
                for _, row in top_10.iterrows():
                    f.write(f"{row['Model']} ({row['Split']}): ")
                    f.write(f"Acc={row['Accuracy']:.4f}, F1={row['F1_Score']:.4f}\n")

                f.write("\nPERFORMANCE BY SPLIT:\n")
                f.write("-" * 22 + "\n")
                for split in self.summary_df['Split'].unique():
                    split_data = self.summary_df[self.summary_df['Split'] == split]
                    f.write(f"\n{split}:\n")
                    f.write(f"  Average Accuracy: {split_data['Accuracy'].mean():.4f}\n")
                    f.write(f"  Best Accuracy: {split_data['Accuracy'].max():.4f}\n")
                    f.write(f"  Worst Accuracy: {split_data['Accuracy'].min():.4f}\n")

        print(f"Analysis report saved to: {report_file}")


def main():
    """Main execution function"""
    print("YOUTUBE SPAM DETECTION - COMPREHENSIVE MODEL TESTING")
    print("="*60)

    # Configuration
    test_dataset_path = "../testing_30102024/youtube_comments_i6IOiUi6IYY_123123.xlsx"
    models_base_dir = "../testing_03112024"
    output_dir = "testing"

    # Train/test splits to test
    train_test_splits = [25, 30, 35]

    # Initialize tester
    tester = YouTubeSpamModelTester(
        test_dataset_path=test_dataset_path,
        models_base_dir=models_base_dir,
        output_dir=output_dir
    )

    # Run comprehensive testing
    tester.run_comprehensive_testing(train_test_splits)

    print("\nTesting completed successfully!")


if __name__ == "__main__":
    main()
